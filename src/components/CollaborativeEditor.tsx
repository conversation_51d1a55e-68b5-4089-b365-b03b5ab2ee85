import { useQuery } from 'convex/react'
import { useBlockNoteSync } from "@convex-dev/prosemirror-sync/blocknote";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import { api, components } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Undo,
  Redo,
  Lock
} from "lucide-react";
import { DocumentHeader } from "./DocumentHeader";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { Separator } from "./ui/separator";
import { Card, CardContent } from "./ui/card";

interface CollaborativeEditorProps {
  documentId: Id<"documents">;
}

export function CollaborativeEditor({ documentId }: CollaborativeEditorProps) {
  const sync = useBlockNoteSync(api.prosemirror, documentId);
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const canCreate = useQuery(api.documents.canCreateDocuments);

  if (sync.isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-muted-foreground">Loading editor...</span>
      </div>
    );
  }

  if (!sync.editor) {
    // Check if user has permission to create documents
    if (canCreate === false) {
      return (
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Lock className="h-6 w-6" />
            <span className="text-lg">Access Restricted</span>
          </div>
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              You don't have permission to create documents.
            </p>
            <p className="text-sm text-muted-foreground">
              Anonymous users cannot create documents. Please sign up for an account to create and edit documents.
            </p>
          </div>
        </div>
      );
    }

    // Check if user has permission to access this specific document
    if (permission === null) {
      return (
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Lock className="h-6 w-6" />
            <span className="text-lg">Document Not Found</span>
          </div>
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              This document doesn't exist or you don't have permission to access it.
            </p>
          </div>
        </div>
      );
    }

    // Only show create button if user has permission to create documents
    if (canCreate?.canCreate === true) {
      return (
        <div className="flex items-center justify-center h-64">
          <Button
            onClick={() => sync.create({
              type: "doc",
              content: []
            })}
            size="lg"
          >
            Create Document
          </Button>
        </div>
      );
    }

    // Show message for users without create permissions
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Lock className="h-6 w-6" />
          <span className="text-lg">No Document Selected</span>
        </div>
        <div className="text-center space-y-2">
          <p className="text-muted-foreground">
            There is currently no document content to be edited or viewed
          </p>
        </div>
      </div>
    );
  }

  // Check if user has write permission for the editor
  const isReadOnly = permission?.permission === "read";

  return (
    <div className="w-full h-full space-y-4">
      <DocumentHeader documentId={documentId} />
      <Card>
        {!isReadOnly && <EditorToolbar editor={sync.editor} />}
        {isReadOnly && (
          <div className="flex items-center gap-2 p-3 border-b bg-muted/30">
            <Lock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              You have read-only access to this document
            </span>
          </div>
        )}
        <div className="min-h-[500px] p-6">
          <BlockNoteView
            editor={sync.editor}
            theme="light"
            className="prose prose-sm max-w-none"
            editable={!isReadOnly}
          />
        </div>
      </Card>
    </div>
  );
}

function EditorToolbar({ editor }: { editor: any }) {
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");

  const handleBold = () => {
    editor.focus();
    editor.commands.toggleBold();
  };

  const handleItalic = () => {
    editor.focus();
    editor.commands.toggleItalic();
  };

  const handleUnderline = () => {
    editor.focus();
    editor.commands.toggleUnderline();
  };

  const handleBulletList = () => {
    editor.focus();
    editor.commands.toggleBulletList();
  };

  const handleOrderedList = () => {
    editor.focus();
    editor.commands.toggleOrderedList();
  };

  const handleHeading = (level: 1 | 2 | 3) => {
    editor.focus();
    editor.commands.toggleHeading({ level });
  };

  const handleLink = () => {
    const selection = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(selection.from, selection.to);

    if (selectedText) {
      setShowLinkDialog(true);
    } else {
      alert("Please select text first to create a link");
    }
  };

  const insertLink = () => {
    if (linkUrl) {
      editor.commands.setLink({ href: linkUrl });
      setShowLinkDialog(false);
      setLinkUrl("");
    }
  };

  const handleUndo = () => {
    editor.commands.undo();
  };

  const handleRedo = () => {
    editor.commands.redo();
  };

  return (
    <>
      <div className="flex items-center gap-1 p-3 border-b bg-muted/50">
        <Button variant="ghost" size="sm" onClick={handleUndo} title="Undo">
          <Undo className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleRedo} title="Redo">
          <Redo className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleBold} title="Bold">
          <Bold className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleItalic} title="Italic">
          <Italic className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleUnderline} title="Underline">
          <Underline className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={() => handleHeading(1)} title="Heading 1">
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => handleHeading(2)} title="Heading 2">
          <Heading2 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => handleHeading(3)} title="Heading 3">
          <Heading3 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleBulletList} title="Bullet List">
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleOrderedList} title="Numbered List">
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-2 h-6" />

        <Button variant="ghost" size="sm" onClick={handleLink} title="Insert Link">
          <Link className="h-4 w-4" />
        </Button>
      </div>

      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              type="url"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
              placeholder="Enter URL..."
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLinkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={insertLink}>
              Insert
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
